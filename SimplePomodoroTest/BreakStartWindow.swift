import Cocoa

/// Окно уведомления о начале отдыха с таймером и напоминанием
class BreakStartWindow: NSWindow {
    
    // Колбэки для действий
    var onSkipBreak: (() -> Void)?
    var onStartBreak: (() -> Void)?
    
    // UI элементы
    private var timeLabel: CustomTimeView!
    private var activityWarningLabel: NSTextField!
    private var timer: Timer?
    private var timeRemaining: TimeInterval = 15 * 60 // 15 минут
    
    override init(contentRect: NSRect, styleMask style: NSWindow.StyleMask, backing backingStoreType: NSWindow.BackingStoreType, defer flag: Bool) {
        super.init(contentRect: NSRect(x: 0, y: 0, width: 420, height: 160),
                  styleMask: [.borderless],
                  backing: .buffered,
                  defer: false)

        setupWindow()
        setupUI()
    }
    
    private func setupWindow() {
        print("🌿 BreakStartWindow: Настройка окна начала отдыха")

        // Настройка окна
        self.isOpaque = false
        self.backgroundColor = NSColor.clear
        self.level = .floating
        self.hasShadow = true
        self.isMovableByWindowBackground = true
    }

    func positionRelativeToStatusItem(statusItemFrame: NSRect) {
        print("🌿 BreakStartWindow: Позиционирование возле status item")

        let windowWidth = self.frame.width
        let windowHeight = self.frame.height

        // Позиционируем под status item с небольшим отступом
        let x = statusItemFrame.midX - windowWidth / 2
        let y = statusItemFrame.minY - windowHeight - 8

        self.setFrameOrigin(NSPoint(x: x, y: y))
    }
    
    private func setupUI() {
        print("🌿 BreakStartWindow: Создание UI для начала отдыха")

        // Создаем основной контейнер с градиентным фоном
        let containerView = NSView()
        containerView.wantsLayer = true
        
        // Создаем градиентный слой
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            NSColor(red: 0.2, green: 0.6, blue: 0.4, alpha: 0.95).cgColor, // Зеленый
            NSColor(red: 0.1, green: 0.4, blue: 0.3, alpha: 0.95).cgColor  // Темно-зеленый
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 1)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0)
        gradientLayer.cornerRadius = 12
        containerView.layer = gradientLayer

        // Заголовок
        let titleLabel = NSTextField(labelWithString: "🍃 Время отдыха!")
        titleLabel.font = NSFont.systemFont(ofSize: 18, weight: .bold)
        titleLabel.textColor = NSColor.white
        titleLabel.alignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false

        // Подзаголовок с инструкциями
        let subtitleLabel = NSTextField(labelWithString: "Закройте ноутбук и отдохните")
        subtitleLabel.font = NSFont.systemFont(ofSize: 13, weight: .medium)
        subtitleLabel.textColor = NSColor.white.withAlphaComponent(0.9)
        subtitleLabel.alignment = .center
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false

        // Таймер - используем кастомный view для гарантированного зеленого цвета
        timeLabel = CustomTimeView()
        timeLabel.translatesAutoresizingMaskIntoConstraints = false
        timeLabel.updateTime(formatTime(timeRemaining))

        // Предупреждение об активности (скрыто по умолчанию)
        activityWarningLabel = NSTextField(labelWithString: "⚠️ Компьютер активен во время отдыха")
        activityWarningLabel.font = NSFont.systemFont(ofSize: 11, weight: .medium)
        activityWarningLabel.textColor = NSColor.yellow
        activityWarningLabel.alignment = .center
        activityWarningLabel.translatesAutoresizingMaskIntoConstraints = false
        activityWarningLabel.isHidden = true

        // Кнопки
        let skipButton = createButton(title: "Пропустить", color: .gray, isSmall: true)
        skipButton.target = self
        skipButton.action = #selector(skipBreakClicked)

        let okButton = createButton(title: "Понятно", color: .white, isSmall: false)
        okButton.target = self
        okButton.action = #selector(startBreakClicked)

        // Добавляем все элементы
        containerView.addSubview(titleLabel)
        containerView.addSubview(subtitleLabel)
        containerView.addSubview(timeLabel)
        containerView.addSubview(activityWarningLabel)
        containerView.addSubview(skipButton)
        containerView.addSubview(okButton)

        self.contentView = containerView

        // Настройка constraints
        NSLayoutConstraint.activate([
            // Заголовок
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 16),
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),

            // Подзаголовок
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 4),
            subtitleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            subtitleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),

            // Таймер
            timeLabel.topAnchor.constraint(equalTo: subtitleLabel.bottomAnchor, constant: 12),
            timeLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            timeLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),

            // Предупреждение об активности
            activityWarningLabel.topAnchor.constraint(equalTo: timeLabel.bottomAnchor, constant: 8),
            activityWarningLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            activityWarningLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),

            // Кнопки
            skipButton.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -16),
            skipButton.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            skipButton.widthAnchor.constraint(equalToConstant: 80),
            skipButton.heightAnchor.constraint(equalToConstant: 24),

            okButton.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -16),
            okButton.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),
            okButton.widthAnchor.constraint(equalToConstant: 100),
            okButton.heightAnchor.constraint(equalToConstant: 32),
        ])

        // Обновляем размер градиента
        DispatchQueue.main.async {
            gradientLayer.frame = containerView.bounds
        }
    }

    private func createButton(title: String, color: NSColor, isSmall: Bool) -> NSButton {
        let button = NSButton()
        button.title = ""
        button.isBordered = false
        button.wantsLayer = true
        button.translatesAutoresizingMaskIntoConstraints = false

        // Создаем градиентный фон для кнопки
        let gradientLayer = CAGradientLayer()
        if color == .white {
            gradientLayer.colors = [
                NSColor.white.withAlphaComponent(0.9).cgColor,
                NSColor.white.withAlphaComponent(0.7).cgColor
            ]
        } else {
            gradientLayer.colors = [
                color.withAlphaComponent(0.6).cgColor,
                color.withAlphaComponent(0.4).cgColor
            ]
        }
        gradientLayer.cornerRadius = isSmall ? 6 : 8
        button.layer = gradientLayer

        // Настройка текста
        let textColor = color == .white ? NSColor.black : NSColor.white
        button.attributedTitle = NSAttributedString(
            string: title,
            attributes: [
                .foregroundColor: textColor,
                .font: NSFont.systemFont(ofSize: isSmall ? 11 : 13, weight: .medium)
            ]
        )

        return button
    }

    // MARK: - Управление таймером

    func startTimer() {
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateTimer()
        }
    }

    func stopTimer() {
        timer?.invalidate()
        timer = nil
    }

    private func updateTimer() {
        timeRemaining -= 1
        updateTimeRemaining(timeRemaining) // Используем наш новый метод с правильным окрашиванием

        if timeRemaining <= 0 {
            stopTimer()
            startBreakClicked()
        }
    }

    func updateTimeRemaining(_ time: TimeInterval) {
        timeRemaining = time
        let timeText = formatTime(time)
        timeLabel.updateTime(timeText)
    }

    func showActivityWarning() {
        activityWarningLabel.isHidden = false
    }

    func hideActivityWarning() {
        activityWarningLabel.isHidden = true
    }

    private func formatTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    // MARK: - Actions

    @objc private func skipBreakClicked() {
        print("🌿 BreakStartWindow: Пропустить отдых")
        stopTimer()
        hideWithAnimation {
            self.onSkipBreak?()
        }
    }

    @objc private func startBreakClicked() {
        print("🌿 BreakStartWindow: Начать отдых")
        stopTimer()
        hideWithAnimation {
            self.onStartBreak?()
        }
    }

    // MARK: - Анимации

    func showWithAnimation() {
        self.alphaValue = 0.0
        self.makeKeyAndOrderFront(nil)

        // Запускаем таймер при показе окна
        startTimer()

        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.3
            context.timingFunction = CAMediaTimingFunction(name: .easeOut)
            self.animator().alphaValue = 1.0
        })
    }

    func hideWithAnimation(completion: @escaping () -> Void) {
        NSAnimationContext.runAnimationGroup({ context in
            context.duration = 0.2
            context.timingFunction = CAMediaTimingFunction(name: .easeIn)
            self.animator().alphaValue = 0.0
        }, completionHandler: {
            self.orderOut(nil)
            completion()
        })
    }
}

// MARK: - Кастомный view для отображения времени с гарантированным зеленым цветом
class CustomTimeView: NSView {
    private var timeText: String = "15:00"
    private let greenColor = NSColor(red: 0.5, green: 0.8, blue: 0.5, alpha: 1.0)
    private let font = NSFont.monospacedDigitSystemFont(ofSize: 24, weight: .bold)

    override func draw(_ dirtyRect: NSRect) {
        super.draw(dirtyRect)

        // Очищаем фон
        NSColor.clear.setFill()
        dirtyRect.fill()

        // Рисуем текст зеленым цветом
        let attributes: [NSAttributedString.Key: Any] = [
            .foregroundColor: greenColor,
            .font: font
        ]

        let attributedString = NSAttributedString(string: timeText, attributes: attributes)
        let textSize = attributedString.size()

        // Центрируем текст
        let textRect = NSRect(
            x: (bounds.width - textSize.width) / 2,
            y: (bounds.height - textSize.height) / 2,
            width: textSize.width,
            height: textSize.height
        )

        attributedString.draw(in: textRect)
    }

    func updateTime(_ time: String) {
        timeText = time
        needsDisplay = true // Перерисовываем view
    }
}
