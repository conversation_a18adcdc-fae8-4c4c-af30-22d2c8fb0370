
# 0.1 - ✅
- [x] Timer
- [x] Base stats 
    - [x] weeks
    - [x] days
- [x] Projects
    - [x] fix  "create projects"
    - [x] Нужно добавить возможность удаления проекта, а то кнопка как будто не активна кнопка удаления.
    - [x] Возможность редактирования проекта
    - [x] Убрать "Выбрать" 
        и сделать чтобы выбрать проект можно было по нажатию в любом месте. Но для чего вообще выбирать? Наверное по клику в любом месте будет открываься окно редактирования. 
    - [x] Шапка 
        Дальше нужно заголовок поправить. У нас заголовок слишком высоко уехал, нужно его поместить на уровень кнопки создать проект. Заголовок управления проектами нужно поместить на уровень кнопки создать проект. А еще лучше чуть-чуть кнопку создать проект приподнять и на этот уровень поместить заголовок управления проекта.
    - [x] Краткая инфа о проекте другая и должна отражать реальные данные (часов, дата старта проект завершен? (статус преокта))
    - [x] Фикс: выравнивание проектов не по низу окна с проектами, а по верху. Странно почему по умолчанию по низу выровняось. 
    - [x] Компактное расположение элементов внутри блока. И не по вертикали а по горизонтали.  
    - [x] Избранные проекты наверху над неизбранными. При добавлении в избранное у он сразу перемещается наверх.
    - [x] Перемещение проектов. Перетаскиванием. И это будет влиять на порядок в контекстном меню. Если в избранном на первом, о и в контекстном меню на первом 
        - [ ] Так, еще я заметил баг в перетаскивании проектов. То есть, если надо переместить какой-то проект на первое место, то вот эта полоска, куда надо переместить, она только между проектами. А вот между, точнее, перед первым или после последнего такой полоски нет и вообще непонятно, как туда переместить. Вот мне надо, например, третье на первое место переместить. А я этого не могу, мне полоски не получается и без полоски перемещение не работает.
    - [x] рабочие vs личные? 
        Надо подумать отдельно. Чтобы потом в статистике можно было показать "только рабочие" 
        Может потом просто сделать возможность редактировать списки. Учеба, хз что им надо, наука, хобби
    - [х] Сделать проект "без категории" - пока вручную
    - [x] В смене названия проекта это не учитывается в контекстном меню, в контекстном меню также старое название. Надо, чтобы после смены названия моментально это отображалось в контекстном меню.
    - [x] То же самое с приоритетом, но почти то же самое. То есть после обновления перетаскивания, перетаскивание порядок не меняется моментально после перетаскивания. В конце концов на меню порядок старый. Не знаю, что нужно сделать, чтобы моментально порядок менялся в контекстном меню
    - [x] Не работает установление иконки, то есть я хочу например поменять иконку, как ее задать. Я не могу, я ввожу стандартные глобовые е, у меня просто е показывает, то есть я не могу иконку эмоций выбрать. Я могу конечно из буфера ее вставить, но это по-моему не выход.
    - [x] В контекстном меню иконка проекта приоритет (если есть). А не иконка категории. И только если нет иконки проекта - показываем иконку категории. 
- [x] Сделать блок для идей по результатам тестирования
- [x] Нормальное отображение статистических показателей 
    - [x] Среднее в день. Там если 0.3, то он показывает 3. 
    - [x] Время старта тоже показывает без ":"
- [x] Убрать анимацию кнопок из попапа
- [x] Порядок в контесктном меню
    - [x] Удалить 3 пункта связанных с "Демо-данные" из контекстного меню
    - [x] Убрать "другие проекты"
    - [x] Убрать "демо новое окно завершения"
    - [x] ПЕРЕМЕСТИТЬ "тестовый запуск"  в самый низ, над "выход". И обособить разделителем сверху
    - [x] Добавите emoji для всех элементов меню контекстного. му, где их еще нет. То есть настройки, статистика, управление проектами и тестовый запуск. И выход.
- [x] Проекты в окне статистики. В той области где выбор перида. После выбора дня, недели, месяца. В том же стиле. 
    По-умолчанию "Все". При нажатии в окне можно выбрать галочками проекты и типы (рабочий, личный и пр)
- [x] Коммит



# 0.2 - ▶️ `по результатам использования`
## A-grade
- [ ] `0.2.1` После начала отдыха (после нажатия кнопки "take a break") сделать таймер отдыха (17 минут или 90 минут после 4 интервалов) - а то многие нажмут "take a break" а сами работают. Или сидят за компом. Это не отых. Таймер меняет цвет на приятный зеленый, но не кислотный. А иконка вместо помодоро превращается в смайл отдыхающего emoji, или recovery. Или полная батарейка. Или листики растения на ветру. 🍃 ⏱️ 🔋 ⏳ 🧘🏼‍♂️ 🔄
    - [x] И напоминать закрыть ноутбук и идти гулять (если он сидит за компом и что-то делает). Например через каждую минуту. 
    - [x] И можно отслеживать был ли открыт ноут во время отдыха, были ли какие-то действия и отображать потом в статистике и в рекомендациях. В расширеной статистике. Если 90% времени он провел вне ноута, то это ок. 
    - [x] Не 15 > 17 минут. После 4 интервалов отдых 90 минут. Но по запросу. Спрашиваем сколько отжохнуть. Коротких или длинный брейк. Можно уже начиная с 3-го спрашивать.
    - [x] Баг? Кстати, когда идет выбор Long или Short, там как будто бы таймер переработки замирает на 0:01. Это баг. Пока человек не определился, время переработки увеличивается. 
    - [x] Кнопки снизу. Не меняя ширины кнопок. Текст сверху. Только 1 фраза. Без кол-ва интервалов. И в конце высоту уменьшить, КАК  ОРИГИНАЛЬНОМ ОКНЕ СДЕЛАТЬ. 
    - [x] `надо тестить` Теперь давай вернем обратно показывать этот выбор только после третьего интервала. И у меня вот вопрос, а если он использовал большой интервал после третьего интервала, то ему в четвертом интервале уже не будет это показываться, правильно? Или начиная с третьего, каждый раз будет показываться? Даже несмотря на то, что он уже выбрал длинный интервал. 
    - [x] Как отслеживать статистику того как он отдыхал? Провоерять каждую минуту был ли активен? И считать в процентах в итогах отдыха? Причем надо учитывать длинный интервал. То есть его тоже как-то надо считать в таком же ключе, чтобы он также считался как и короткий. Ну в процентах, наверное, я думаю. То есть 100% от длинного, 100% от короткого.
        - [x] Добавить расширенную статистику в экан статистики. У нас уже есть окно статистики со статистическими элементами, там их 6 блоков или сколько-то. Может быть мы сделаем такой стрелочку вниз или как-то я не знаю, посередине или кнопочку, со стрелочкой, типа показать все и будут показаны еще дополнительно и вот в данном случае вот этот один, то есть, ну то есть, я не знаю как это 100% отдых или как, то есть, у отдыха время интервал или как, не знаю, надо подумать, как назвать его, вот и его надо как-то так поместить, чтобы у него ширина была не на всю длину этого окна, да, как вот как-то вот максимальную ширину что ли ему задать, я не знаю, надо подумать.
        - [x] В логическом блоке данные обновляются только после завершения отдыха, а не так, что он даже ни одного отдыха не начал, а у него уже показывает среднее качество отдыха 100% Или там он начал, например отдыхать, одну минуту отдохну, у него уже показывает там 100% То есть считается только после завершения сессии отдыха.
    - [ ] После истечения таймера отдыха не хватает окна "начать интервал?" с выбранным последним проектом. И возможностью проект изменить в 1-2 клика. Ты же это сделал? Можно потестить?
        - [ ] Что происходит после нажатия "later"? Через 5 или 15 минут? А может справшивать? 5 или 15 минут? Или это слишком навязчиво? 
        - [ ] При нажатии на проект надо сделать возможность выбрать проект. Сейчас у нас тупо открывается страницы проектов. А надо создать какое-то просто диалоговое окно, чтобы в один клик можно было выбрать проект для следующего интервала.

    - [ ] Поправить зеленое окно напоминания во время отдыха, если есть активность. надо и таймер сделать больше, и пониже его опустить. И с кнопками что-то решить. "понятно" "пропустить", сделать другими. Надо чо-то одно наверное оставить. И само окно меньше по высоте сделать. И там же еще есть текст "обнаружена активность". Его тожде надо куда-то поместить повыше.  
    - Досрочное заершение отдыха и начало нового интервала (+предупреждение, и будет записано в статистику если злоупотребляет)
- [ ] Актуализировать цветовые индикаторы в статистический блок, чтобы они отражали реальные данные.
- [ ] Более агрессивные сообщения во всплывающем окне (можно утвердить их список)
    - Чтобы они говорили о важности перерывов
- Завершение проектов
    - Но как определить настоящее завершение проекта от ложного завершения, которое вроде бы завершили так типа Я тем больше заниматься не буду, но на самом деле не добились, что хотели, как бы бывает как часто?

## B-grade (later)
- [ ] Пользователи. Авторизация и БД. 
- [ ] Возможность выбирать выходные (и рабочие дни)
- [ ] Если буду игнорировать эти напоминания. 
    То можно будет что-то с этим делать. Например на весь экран окно, с невозможностью закрыть и с кратким напоминанием к чему ведёт переработка (потеря уверенности, снижение продуктивности, тревожность, отсутствие наслаждения). Сделай вдох и или погуляй. Никуда от тебя работа не денется.
- [ ] Как начинать работу с нуля
    два элемента - работа - первое дело дня. И практика Павла психитра - делать работу или ничего 
- [ ] 📊 Улучшение статистических блоков
    - На недельном 
    - На дневном
    - Без категории (любые идеи по статистическим блокам) - чем больше данных собираем, тем легче будет анализировать
        - Среднее время отдыха (поможет получше понять жффеткивность)
- [ ] В графике отразить легенду зеркально . Или вообще убрать
- [ ] Статистические точки (цветные) сделать зависимыми от результатов
- [ ] Сделать сравнение с прошлым периодом (лучше, хуже, на сколько в %)
- [x] Прибраться в шапке. 
    - [x] Сделать выбор периода в одну строку. Одной высотой. И справ добавить икноку профиля. Стрики и настройки
    - [x] Выровнять отступы
- [ ] Статистика только по проекту (от начала до конца, без привязки ко времени) 
    - Частично реализовано на странице проектов, но нужна более подробная статистика в формате статистики со всеми статистическими плашками и чартами.
- [ ] галочка "Показывать завершенные проекты". Или архивные? И вообще сделать выбор при редактировании проекта "завершен или нет"
- [ ] Статус проекта "завершено". И в интерфейс галочку "скрыть завершенные".
- ВО ВРЕМЯ СЕССИИ:
    - Иконка проекта уходит вправо от таймера, а не слева
- Можно еще отслеживать общее количество проведенного времени за компьютером и потом смотреть сколько было интервалов. И как-то это учитывает то же, что, мол, дружок, смотри, ты работал столько-то, а за компьютером просидел два раза больше. О каком отдыхе вообще может идти речь и восстановление, пока непонятно в какой это статистический блок добавить, но отслеживать это можно посмотреть и вообще можно как можно больше всего отслеживать.

## C-grade
- В настпойках "автоматическое применение рекомендацмй. 
    - Ну то есть, например, поняли мы, что слишком ему много будет 52 минуты интервала у него, он с этим-то не справляется и ему автоматически будет понижаться интервал. Если галочка этого набирает, то тогда не будет, тогда будет как бы всплывающее сообщение. Типа, вот мы хотим понизить интервал, согласны, не согласны и так далее.
- Лонг брейк можно привязать ко времени. Например ближе к 12 или 1 пм

## Непонятно (но проблема есть)
- [ ] Что если надо прекратить на половине интервала? Кушать например. 
- [ ] Идет ли таймер если закрыть крышку ноута? Останавливается. Надо чтобы сбрасывался?
-  Но как определить настоящее `завершение проекта` от ложного завершения, которое вроде бы завершили так типа Я тем больше заниматься не буду, но на самом деле не добились, что хотели, как бы бывает как часто?








# OLD (messy)

## 0.1 - pomodoro
- [x] base timer
- [x] Bug: после нажатия +1 мин не появилось окно с напоминанием через 1 минуту. Нужно починить
- [x] Если переработка, то текст таймера меняется. Слегка желтый после 1-й минуты. Орандевый после 3-х минут. И красный после 5 минут. И темно бардовый после 10 минут. После 15 минут появляется смайлик с закипающим мозгом 😤
- Анимация кнопок "заверщить" и продлить"
- [x] Base stats (сколько интевалов полноценных (именно 52 минуты прошло, а не заверешнных вручную ) за сегодня, вчера, неделю, месяц, год, всего за все время)
    - [x] Страница статистики. Пока что простая. Тупо сколько интервалов за день, неделю, месяц, год, всего за все время. Графики не нужны. Тоже самое для переработки. 
    - [x] Как анализировать и давать рекомендации? Спроси ЛЛМ. Анализ постоянства, поздно начинает, перерабатыает, риск выгорания. 
- [ ] Графические элементы. Непонятно пока как. Либо просто в виде интервалов, с переработками. Либо статистику по дням. Например если это неделя, то 7 столбиков. И в каждом. Тут надо сначала подумать для недель как показать. Не надо сразу для всех периодов думать. Тут много вариаций даже для недели. Как показывать? Просто интервалы? Нет. 
    - [x]разные цвета в зависимости от кол-ва интервалов. До 1-2 синий. От 3 до 5 зеленый.  6 желтый.  7 оранжевый. От 8 и выше красный. 
    - Легенда по дням (ось X) и кол-ву интервалов Y























- [ ] Проекты
    - Сделать проект "без категории". Третий. Когда просто открываем комп и непонятно что делаем. 
- [ ] Контекстное меню не стандартное, а кастомизированное. Чтобы дизайн можно было настраивать. И Там сделать разные версии проектов. С возможностью быстрой смены проекта.  И СПИСОК ЗАДАЧ GG НА ДЕНЬ - ЧЕКЛИСТЫ ПО ПЛАНКАМ. Кол-во интервалов сегодня. СТрики может быть. Скор. (мини-дашборд короче). Да, многих элементов нету, но надо создать болванку, на которую потом буду насаживать. 













- Для каждого статистического блока можно сделать четыре вида градации, красненькую, оранжевую, желтую, зеленую. То есть в виде батарейки или как у меня вью мема есть для индикатор новых слов.
- Вообще подумать как сделать для дней, как для неделей, месяцев и лет. Разные или одинаоквые элементы будут? 
    - [ ] Статистика для дней. Там иначе все будет. Подумать как. Я буду тестить каждый день на реальной работе. И смотреть как он спавится. 
        - Если все хорошо, то говорить что все хорошо. Нет рекомендаций, продолжай в том же духе
    - чарты (если месячные, то можно сделать выборку по дням, а можно по неделям). Годовые тоже самое. Можно по месяцам, можно по неделям. 
- Подумать какие показатели считать в статистике. Более детально. 
    - Может сделать Score-постоянства справа обособленным. Как стрелка бензина. Или как линию в пространстве, чтобы удерживать ее. 

- 🌡️ SCORE
    Возможно будет состоять из нескольких показателей
    - 1. Постоянство (сколько дней за неделю работал) 4 из 5? Значит 0.8 (80%)
    - 2. Стабильность итервалов (нет резких скачков)
    - 2.5. Нормальное кол-во интервалов в день. 4-5. Чем меньше, тем хуже показатель. Чембольше, тем тоже хуже.  
    - 3. Нет переработок 
    И отследивать его во времени. По неделям. И выводить в дашборде как кривую. Кстати, можно его также оценивать и во времени.
- Проблема - нет выходных. В анализ недели и месяца. В настройки - работать в выходные? По крайней мере в субботу. Да, короткий день. Но лучше не надо. 
- Критические рекомендации. И допустимые. Например перербаотка +5-10 минут к интервалу это ок. 10-20 это уже медиум. Более это уже хай-риск. Потом По кол-ву интервалов в день. 6 это уже низкий риск. Более это уже критично. И так каждый элемент разобрать. Время начала. 8-9 это лоу. 9-10 это медиум. После 10 это уже хай риск. Возможно кстати давать в самих плашках оценку риска. И рекомендации. А то таблицы прям не очень. 
- Проекты. Описать что хочу и Спросить как
- Не даст начать интервал, если не прошло время отдыха. 17 минут для коротких и 60-90 для длинного. 
    - + сообщение, что не прошло время отдыха
    - Кнопка "начать все равно, осознаю риски"
- Звуки 
    - заверщения интервала
    - продлений (1, 2, 3) - агрессивнее
    - возможность выбора звука в настрйоках
- Конфети после звершения интервала
- Анимации окна
- Время переработки (дублировать таймер) в окно 
- После завершения отдыха предложит начать интервал, в таком же всплывающем окне, но другом.
- окно всплывающее более прозрачным. И с глассморфизмом
- Если окошко открыто в оверворке, то можно сделать его анимацию короткую, что мол иди отдыхай
- Кнопка "начать отдых" тоже нужна. С таймером. Не у всех часы есть. Но надо сделать интеграцию с телефоном. Потому что если ноут закрыт то что? Но это потом. Пока для себя делаю. 
- БД и авторизация


Так, некоторые элементы статистики можно скрыть, такие как например время начала, среднее время, там окончание работ. Они как бы, ну или там сделать их поменьше, просто они такие не основные, но они используются для рекомендаций. Тоже, потому что если там человек заканчивает работу там ночью, то ну как бы хреново ему будет, потом быстро. Вот и 

Сами рекомендации тоже давать можно как бы кратко, то есть сделать просто их список, приоритизировать, но это в документации, а в самом приложении давать кратко и там давать какую-то типа там, смотреть, читать подробнее или там, ну не знаю, как это сделать красиво. Там уже подробнее рассказывать, для чего это нужно, почему это важно и уже прям совсем, если они хотят подробнее, то уже там переводить на какую-то там страничку в интернете, где рассказывать, почему это вообще важно, прям там уже, ну обычная статья, типа там мини-превычки, все такое, то есть получается краткая информация, просто краткий, сначала краткая, прям краткая, краткая, потом чуть поподробнее, если надо и уже прям совсем подробнее тоже на сайте.



## 0.2 - авторизация для сохранения статистики

## 0.3 - score постоянства
- Как считать? Не знаю. Расскажи что думаешь для ЛЛМ и спроси решение


===
- Короткий интервал (25 минут) - прям в контекстном меню, под основным. И который кстати будет перерастать в обычный
- После 12 создаются короткие

## Адаптивный интервал
- Для тех, кто не может начать - 1-5 минут. Или вообще прям целая программа влючения в поток. Нажимает SOS и вводит в поток. Хотя можно и без нажатия SOS это делать. Видит что давно не работал. Значит выпал очевидно. Значит надо возвращать. Начать с малого и наращивать, собирая обратную связь. 

    - То есть программа видит, она там, ты выпал, да, она предлагает укороченный интервал. Если ты и его не начинал, то она предлагает еще более укороченный, там, 5 минут поработать, там, и каждый раз она предлагает, давай там, хотя бы там, задача как-то там, посмотрим, что у нас есть, на чем мы застряли, или там, если она там, чувствую, что застрял, вот, давай попробуем алгоритм преодоления барьеров и так далее, то есть разные варианты пробовать. 
    - Кстати, можно вгнедрить методику Павла психиатора - делай дело или ничего. С мини-обучением. 

Что-то неплохо было бы вот так вот начать работать, да, то есть человек сидит, не может собраться и ему так баться, сообщения всплывать. Ну что-то там я вижу ты уже два дня не можешь начать, давайте по работе. И он говорит, ну и там адаптируется интервал уже естественно, там хотя бы 15 минут ему предлагается. Он говорит, нет, типа у меня нету идей там или что-нибудь такое. И ему там вариант предлагается, там сразу же, давай там поищем идей, там и все такое. Или как-то там я застрял, то есть в зависимости от ему подставляются варианты. И то есть увлекает его в процесс, вот это прям очень важно. То есть мы из любого дерьма его вот так скажем, из переработок, из барьеров, из нет идей и так далее и тому подобное.

- Можно вводить мини-обучения. 
    - Как отдыхать (без тиктока, экранов)

## Как быть с опросами? 
Непонятно куда из внедрять. Не зочется отвечать на вопросы. Даже мне. А юзерам тем более
Варианты 
    - внедрить вопросы в процессы. И вовлекать.
    - просто вечером задавать один вопрос (устал ли), потом другой 

## Стрики 
Стрики, наверное, будут не в статистике, а просто где-то наверху, как в дуалингу, в принципе, сделаны. И также с темы уведомлений будет каждый вечер, что типа, "Эй, дружок, там осталось только то времени, давай" типа один оторвал хотя бы, если он не сделал, конечно, если сделал, то все хорошо. И также его учим, что типа, стрики это вообще как бы входит в топ-3, чуваков с высокой продуктивностью, то есть они выполняют стри, типа, "это мы вы выполняете". Типа, если вы будете там хотя бы минималочку делать, там каждый день 20 минут, то вы через год все они узнаете. Ну и там можно в кратской мини-привычке книгу пересказать там на паре экранах, а даже на одну.

-------------

# v1 - Task-manager 
Попробовать месяцок, может получится что-то интересное. Для лчиного использования. 
- Сделать анализ всех основных конкурентов (что нравится, что не нравится)
- Но надо будет решить главную проблему - как показывать все правильно без путаницы с тасками и подтасками. 
    - Прям сфокусироваться на этой проблеме, нырнуть в нее. Описать что бесит. И посоветоваться как решить. 


# IDEAS
- Сделать интеграцию с другими моими приложениями. Чтобы он мог во время интервалов например спортом начать заниматься. Или счастье практиковать. И ремайндерсы делать. 